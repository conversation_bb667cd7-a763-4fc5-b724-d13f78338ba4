<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ESP32 Absensi - OTA Management Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 1.1em;
        }
        
        .dashboard {
            padding: 30px;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 25px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        
        .stat-card h3 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .stat-card p {
            opacity: 0.9;
        }
        
        .section {
            margin-bottom: 30px;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
        }
        
        .section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.5em;
        }
        
        .device-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .device-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-left: 4px solid #3498db;
        }
        
        .device-card.online {
            border-left-color: #27ae60;
        }
        
        .device-card.offline {
            border-left-color: #e74c3c;
        }
        
        .device-card.updating {
            border-left-color: #f39c12;
        }
        
        .device-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .device-id {
            font-weight: bold;
            color: #2c3e50;
        }
        
        .status-badge {
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .status-online {
            background: #d4edda;
            color: #155724;
        }
        
        .status-offline {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status-updating {
            background: #fff3cd;
            color: #856404;
        }
        
        .device-info {
            margin-bottom: 15px;
        }
        
        .device-info div {
            margin-bottom: 5px;
            color: #666;
        }
        
        .progress-bar {
            width: 100%;
            height: 10px;
            background: #e9ecef;
            border-radius: 5px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
            transition: width 0.3s ease;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            transition: transform 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        
        .upload-section {
            background: white;
            border-radius: 10px;
            padding: 25px;
            text-align: center;
            border: 2px dashed #ddd;
        }
        
        .file-input {
            margin: 20px 0;
        }
        
        .logs {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 ESP32 Absensi OTA Dashboard</h1>
            <p>Centralized Firmware Management System</p>
        </div>
        
        <div class="dashboard">
            <!-- Statistics -->
            <div class="stats">
                <div class="stat-card">
                    <h3 id="totalDevices">12</h3>
                    <p>Total Devices</p>
                </div>
                <div class="stat-card">
                    <h3 id="onlineDevices">10</h3>
                    <p>Online Devices</p>
                </div>
                <div class="stat-card">
                    <h3 id="updatingDevices">2</h3>
                    <p>Updating</p>
                </div>
                <div class="stat-card">
                    <h3 id="latestVersion">1.3.0</h3>
                    <p>Latest Version</p>
                </div>
            </div>
            
            <!-- Firmware Upload -->
            <div class="section">
                <h2>📦 Upload New Firmware</h2>
                <div class="upload-section">
                    <h3>Upload Firmware Binary</h3>
                    <div class="file-input">
                        <input type="file" id="firmwareFile" accept=".bin" />
                    </div>
                    <input type="text" id="versionInput" placeholder="Version (e.g., 1.3.0)" style="padding: 10px; margin: 10px; border: 1px solid #ddd; border-radius: 5px;" />
                    <br>
                    <button class="btn" onclick="uploadFirmware()">Upload Firmware</button>
                </div>
            </div>
            
            <!-- Device Management -->
            <div class="section">
                <h2>📱 Device Management</h2>
                <div class="device-grid" id="deviceGrid">
                    <!-- Devices will be populated by JavaScript -->
                </div>
            </div>
            
            <!-- System Logs -->
            <div class="section">
                <h2>📋 System Logs</h2>
                <div class="logs" id="systemLogs">
                    [2024-01-08 10:30:15] ESP32-A1B2C3 - Update check requested<br>
                    [2024-01-08 10:30:16] ESP32-A1B2C3 - Update available: 1.2.0 → 1.3.0<br>
                    [2024-01-08 10:30:17] ESP32-A1B2C3 - Download started<br>
                    [2024-01-08 10:30:45] ESP32-A1B2C3 - Download completed (1.2MB)<br>
                    [2024-01-08 10:30:46] ESP32-A1B2C3 - Installation started<br>
                    [2024-01-08 10:31:15] ESP32-A1B2C3 - Update completed successfully<br>
                    [2024-01-08 10:31:16] ESP32-A1B2C3 - Device restarted<br>
                    [2024-01-08 10:31:30] ESP32-A1B2C3 - Online with version 1.3.0<br>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Sample device data
        const devices = [
            {
                id: 'ESP32-A1B2C3',
                status: 'online',
                version: '1.3.0',
                ip: '*************',
                lastSeen: '2 minutes ago',
                rssi: -45,
                updateProgress: 0
            },
            {
                id: 'ESP32-D4E5F6',
                status: 'updating',
                version: '1.2.0',
                ip: '*************',
                lastSeen: '1 minute ago',
                rssi: -52,
                updateProgress: 65
            },
            {
                id: 'ESP32-G7H8I9',
                status: 'offline',
                version: '1.1.0',
                ip: '*************',
                lastSeen: '1 hour ago',
                rssi: -78,
                updateProgress: 0
            }
        ];

        function renderDevices() {
            const grid = document.getElementById('deviceGrid');
            grid.innerHTML = '';
            
            devices.forEach(device => {
                const card = document.createElement('div');
                card.className = `device-card ${device.status}`;
                
                card.innerHTML = `
                    <div class="device-header">
                        <div class="device-id">${device.id}</div>
                        <div class="status-badge status-${device.status}">${device.status}</div>
                    </div>
                    <div class="device-info">
                        <div><strong>Version:</strong> ${device.version}</div>
                        <div><strong>IP:</strong> ${device.ip}</div>
                        <div><strong>RSSI:</strong> ${device.rssi} dBm</div>
                        <div><strong>Last Seen:</strong> ${device.lastSeen}</div>
                    </div>
                    ${device.status === 'updating' ? `
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${device.updateProgress}%"></div>
                        </div>
                        <div style="text-align: center; margin-top: 10px;">
                            Updating... ${device.updateProgress}%
                        </div>
                    ` : ''}
                    <div style="margin-top: 15px;">
                        <button class="btn" onclick="updateDevice('${device.id}')" 
                                ${device.status !== 'online' ? 'disabled' : ''}>
                            Force Update
                        </button>
                        <button class="btn" onclick="rebootDevice('${device.id}')" 
                                ${device.status !== 'online' ? 'disabled' : ''}>
                            Reboot
                        </button>
                    </div>
                `;
                
                grid.appendChild(card);
            });
        }

        function updateDevice(deviceId) {
            console.log(`Triggering update for ${deviceId}`);
            addLog(`Manual update triggered for ${deviceId}`);
            
            // Simulate update process
            const device = devices.find(d => d.id === deviceId);
            if (device) {
                device.status = 'updating';
                device.updateProgress = 0;
                renderDevices();
                
                // Simulate progress
                const interval = setInterval(() => {
                    device.updateProgress += 10;
                    if (device.updateProgress >= 100) {
                        device.status = 'online';
                        device.version = '1.3.0';
                        device.updateProgress = 0;
                        clearInterval(interval);
                        addLog(`Update completed for ${deviceId}`);
                    }
                    renderDevices();
                }, 500);
            }
        }

        function rebootDevice(deviceId) {
            console.log(`Rebooting ${deviceId}`);
            addLog(`Reboot command sent to ${deviceId}`);
        }

        function uploadFirmware() {
            const fileInput = document.getElementById('firmwareFile');
            const versionInput = document.getElementById('versionInput');
            
            if (!fileInput.files[0] || !versionInput.value) {
                alert('Please select a firmware file and enter version number');
                return;
            }
            
            const file = fileInput.files[0];
            const version = versionInput.value;
            
            addLog(`Uploading firmware ${version} (${file.name})`);
            
            // Simulate upload
            setTimeout(() => {
                addLog(`Firmware ${version} uploaded successfully`);
                document.getElementById('latestVersion').textContent = version;
            }, 2000);
        }

        function addLog(message) {
            const logs = document.getElementById('systemLogs');
            const timestamp = new Date().toLocaleString();
            logs.innerHTML += `[${timestamp}] ${message}<br>`;
            logs.scrollTop = logs.scrollHeight;
        }

        // Initialize dashboard
        renderDevices();
        
        // Simulate real-time updates
        setInterval(() => {
            // Update last seen times
            devices.forEach(device => {
                if (device.status === 'online') {
                    device.lastSeen = Math.floor(Math.random() * 5) + ' minutes ago';
                }
            });
            renderDevices();
        }, 30000);
    </script>
</body>
</html>

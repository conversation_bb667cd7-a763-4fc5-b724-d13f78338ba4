# OTA Updates Guide - ESP32 MQTT Absensi

## 🔄 Over-The-Air (OTA) Updates

Panduan lengkap untuk melakukan update firmware secara wireless pada ESP32 MQTT Absensi System.

## 📋 Prerequisites

### Hardware Requirements
- ESP32 dengan firmware v1.2.0+
- WiFi connection yang stabil
- Power supply yang stabil (jangan gunakan battery saat OTA)

### Software Requirements
- Arduino IDE 2.0+ atau PlatformIO
- ESP32 Board Package
- Network access ke device

## 🔍 Check Device Information

### Via Serial Monitor
```bash
# Connect via serial (115200 baud)
ota_info

# Output:
=== OTA UPDATE INFO ===
Firmware Version: 1.2.0
Device Model: ESP32-MQTT-Absensi
Device ID: ESP32-A1B2C3
OTA Status: READY
OTA Hostname: esp32-absensi-A1B2C3
OTA Port: 3232
IP Address: *************
```

### Via MQTT
```bash
# Send command via MQTT
mosquitto_pub -h broker.com -p 8883 --cafile certs.ar \
  -u username -P password -t "absensi/commands" \
  -m '{"command": "ota_info"}'
```

## 🛠️ Update Methods

### Method 1: Arduino IDE

1. **Setup Board**:
   - Tools → Board → ESP32 Dev Module
   - Tools → Partition Scheme → Default 4MB

2. **Find Network Port**:
   - Tools → Port → Look for "esp32-absensi-A1B2C3 at *************"
   - If not visible, check network connectivity

3. **Upload Firmware**:
   - Sketch → Upload (Ctrl+U)
   - Enter password: `absensi2024`
   - Wait for completion

### Method 2: PlatformIO

1. **Configure platformio.ini**:
```ini
[env:esp32dev]
platform = espressif32
board = esp32dev
framework = arduino

# OTA Configuration
upload_protocol = espota
upload_port = esp32-absensi-A1B2C3.local
upload_flags = --auth=absensi2024

# Alternative: Use IP address
; upload_port = *************
```

2. **Upload**:
```bash
pio run --target upload
```

### Method 3: Command Line

```bash
# Using espota.py (included with ESP32 package)
python espota.py -i ************* -p 3232 -a absensi2024 -f .pio/build/esp32dev/firmware.bin

# Using Arduino CLI
arduino-cli upload -p esp32-absensi-A1B2C3.local --fqbn esp32:esp32:esp32
```

## 📊 Update Process Monitoring

### LCD Display
```
Line 1: OTA Update
Line 2: 45%
```

### Serial Monitor
```
Start updating sketch
Progress: 25%
Progress: 50%
Progress: 75%
Progress: 100%
End
```

## ⚠️ Troubleshooting

### Common Issues

| Problem | Cause | Solution |
|---------|-------|----------|
| **Device not found** | Network/mDNS issue | Use IP address instead of hostname |
| **Authentication failed** | Wrong password | Verify password: `absensi2024` |
| **Upload timeout** | Weak WiFi signal | Move closer to router |
| **Partition error** | Wrong partition scheme | Use "Default 4MB" partition |
| **Connection refused** | Device busy/crashed | Reset device, try again |

### Network Discovery
```bash
# Find ESP32 devices on network
nmap -p 3232 ***********/24

# Check mDNS
ping esp32-absensi-A1B2C3.local

# Alternative discovery
arp -a | grep esp32
```

## 🔒 Security Considerations

### OTA Security Features
- **Password Protection**: `absensi2024` (change in production)
- **Network Encryption**: WPA2/WPA3 WiFi security
- **Firmware Validation**: ESP32 built-in signature verification
- **Rollback Protection**: Previous firmware preserved

### Production Security
```cpp
// Change default password in src/main.cpp
ArduinoOTA.setPassword("your-secure-password-here");

// Enable additional security
ArduinoOTA.setPasswordHash("md5-hash-of-password");
```

## 📝 Best Practices

### Before Update
1. **Backup Current Firmware**: Save working .bin file
2. **Test on Development Device**: Never update production directly
3. **Check Network Stability**: Ensure strong WiFi signal
4. **Verify Power Supply**: Use stable power source
5. **Schedule Maintenance Window**: Update during low usage

### During Update
1. **Monitor Progress**: Watch serial output and LCD
2. **Don't Interrupt**: Never power off during update
3. **Stay Connected**: Keep WiFi stable
4. **Be Patient**: Large updates take 2-3 minutes

### After Update
1. **Verify Functionality**: Test all features
2. **Check Version**: Confirm firmware version updated
3. **Monitor Logs**: Watch for errors or issues
4. **Update Documentation**: Record changes made

## 🚀 Advanced OTA Features

### Batch Updates
```bash
# Update multiple devices
for ip in ************* ************* *************; do
  python espota.py -i $ip -p 3232 -a absensi2024 -f firmware.bin
done
```

### Automated Updates
```python
# Python script for automated OTA
import subprocess
import time

devices = [
    {"ip": "*************", "id": "ESP32-A1B2C3"},
    {"ip": "*************", "id": "ESP32-D4E5F6"}
]

for device in devices:
    print(f"Updating {device['id']}...")
    result = subprocess.run([
        "python", "espota.py",
        "-i", device["ip"],
        "-p", "3232", 
        "-a", "absensi2024",
        "-f", "firmware.bin"
    ])
    if result.returncode == 0:
        print(f"✅ {device['id']} updated successfully")
    else:
        print(f"❌ {device['id']} update failed")
    time.sleep(5)  # Wait between updates
```

---

**🔄 Keep your devices updated for latest features and security patches**

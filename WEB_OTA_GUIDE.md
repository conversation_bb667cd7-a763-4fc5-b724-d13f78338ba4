# Web-Based OTA Update Guide

Panduan lengkap untuk mengintegrasikan ESP32 dengan web server Anda untuk OTA updates otomatis.

## 🌐 **Metode OTA Update**

### **1. Local OTA (Arduino IDE/PlatformIO)**
- Update langsung via network
- Untuk development dan testing

### **2. Web-Based OTA (Recommended)**
- Update via web interface
- Integrasi dengan web server Anda
- Automatic update checking

### **3. Remote OTA via API**
- Update otomatis dari server
- Scheduled updates
- Centralized firmware management

## 🔧 **Setup Web Server API**

### **1. API Endpoints yang Dibutuhkan**

#### **Check Update Endpoint**
```
POST https://yourwebsite.com/api/firmware/check
Content-Type: application/json

Request Body:
{
  "device_id": "ESP32-A1B2C3",
  "current_version": "1.2.0",
  "device_model": "ESP32-MQTT-Absensi"
}

Response:
{
  "update_available": true,
  "version": "1.3.0",
  "download_url": "https://yourwebsite.com/api/firmware/download/1.3.0",
  "release_notes": "Bug fixes and new features",
  "mandatory": false
}
```

#### **Download Firmware Endpoint**
```
GET https://yourwebsite.com/api/firmware/download/1.3.0
Authorization: Bearer <token> (optional)

Response: Binary firmware file (.bin)
```

### **2. Contoh Implementasi Server (PHP)**

#### **check_update.php**
```php
<?php
header('Content-Type: application/json');

// Get request data
$input = json_decode(file_get_contents('php://input'), true);
$device_id = $input['device_id'];
$current_version = $input['current_version'];
$device_model = $input['device_model'];

// Log request
error_log("Update check from $device_id, version $current_version");

// Check database for latest firmware
$latest_version = "1.3.0"; // Get from database
$update_available = version_compare($current_version, $latest_version, '<');

$response = [
    'update_available' => $update_available,
    'version' => $latest_version,
    'download_url' => "https://yourwebsite.com/api/firmware/download/$latest_version",
    'release_notes' => 'Performance improvements and bug fixes',
    'mandatory' => false
];

echo json_encode($response);
?>
```

#### **download_firmware.php**
```php
<?php
$version = $_GET['version'] ?? '1.3.0';
$firmware_path = "/path/to/firmware/esp32_absensi_$version.bin";

if (file_exists($firmware_path)) {
    header('Content-Type: application/octet-stream');
    header('Content-Disposition: attachment; filename="firmware.bin"');
    header('Content-Length: ' . filesize($firmware_path));
    readfile($firmware_path);
} else {
    http_response_code(404);
    echo json_encode(['error' => 'Firmware not found']);
}
?>
```

### **3. Contoh Implementasi Server (Node.js)**

#### **server.js**
```javascript
const express = require('express');
const fs = require('fs');
const path = require('path');
const app = express();

app.use(express.json());

// Check for updates
app.post('/api/firmware/check', (req, res) => {
    const { device_id, current_version, device_model } = req.body;
    
    console.log(`Update check from ${device_id}, version ${current_version}`);
    
    const latest_version = "1.3.0"; // Get from database
    const update_available = current_version < latest_version;
    
    res.json({
        update_available,
        version: latest_version,
        download_url: `https://yourwebsite.com/api/firmware/download/${latest_version}`,
        release_notes: 'Performance improvements and bug fixes',
        mandatory: false
    });
});

// Download firmware
app.get('/api/firmware/download/:version', (req, res) => {
    const version = req.params.version;
    const firmwarePath = path.join(__dirname, 'firmware', `esp32_absensi_${version}.bin`);
    
    if (fs.existsSync(firmwarePath)) {
        res.download(firmwarePath, 'firmware.bin');
    } else {
        res.status(404).json({ error: 'Firmware not found' });
    }
});

app.listen(3000, () => {
    console.log('Firmware server running on port 3000');
});
```

## 🔧 **Konfigurasi ESP32**

### **1. Update URL di ESP32**
```cpp
// Edit di src/main.cpp
#define OTA_UPDATE_URL "https://yourwebsite.com/api/firmware/check"
#define OTA_DOWNLOAD_URL "https://yourwebsite.com/api/firmware/download"
```

### **2. SSL Certificate (Production)**
```cpp
// Untuk HTTPS, tambahkan certificate
const char* rootCACertificate = \
"-----BEGIN CERTIFICATE-----\n" \
"MIIDQTCCAimgAwIBAgITBmyfz5m/jAo54vB4ikPmljZbyjANBgkqhkiG9w0BAQsF\n" \
"...\n" \
"-----END CERTIFICATE-----\n";

// Di setupWebOTA()
client.setCACert(rootCACertificate);
```

## 🎮 **Cara Menggunakan**

### **1. Manual Update Check**
```bash
# Via Serial Monitor
check_update

# Via MQTT
mosquitto_pub -h broker.com -p 8883 --cafile certs.ar \
  -u username -P password -t "absensi/commands" \
  -m '{"command": "check_update"}'
```

### **2. Web Interface Update**
```
1. Buka browser
2. Akses: http://192.168.1.100/update
3. Pilih file firmware (.bin)
4. Klik "Update Firmware"
5. Tunggu hingga selesai
```

### **3. Automatic Updates**
- ESP32 akan check update setiap 1 jam
- Jika ada update tersedia, akan download otomatis
- Device akan restart setelah update selesai

## 📊 **Monitoring & Logging**

### **1. Update Status via MQTT**
```json
{
  "device_id": "ESP32-A1B2C3",
  "event": "update_check",
  "current_version": "1.2.0",
  "latest_version": "1.3.0",
  "update_available": true,
  "timestamp": 1640995200
}
```

### **2. Update Progress**
```json
{
  "device_id": "ESP32-A1B2C3",
  "event": "update_progress",
  "progress": 45,
  "status": "downloading",
  "timestamp": 1640995200
}
```

### **3. Update Complete**
```json
{
  "device_id": "ESP32-A1B2C3",
  "event": "update_complete",
  "old_version": "1.2.0",
  "new_version": "1.3.0",
  "status": "success",
  "timestamp": 1640995200
}
```

## ⚠️ **Security & Best Practices**

### **1. Security Measures**
- **HTTPS Only**: Gunakan SSL/TLS untuk semua komunikasi
- **Authentication**: Implementasi API key atau JWT token
- **Firmware Signing**: Verifikasi signature firmware
- **Rate Limiting**: Batasi request per device

### **2. Rollback Strategy**
```cpp
// Simpan versi sebelumnya
String previous_version = preferences.getString("prev_version", "");
preferences.putString("prev_version", FIRMWARE_VERSION);

// Rollback jika update gagal
if (update_failed) {
    // Download previous version
    downloadAndInstallUpdate(previous_version_url);
}
```

### **3. Staged Deployment**
- Test pada 1-2 device dulu
- Gradual rollout (10%, 50%, 100%)
- Monitor error rates
- Automatic rollback jika error rate tinggi

## 🔍 **Troubleshooting**

### **Common Issues**
| Problem | Solution |
|---------|----------|
| **SSL Certificate Error** | Update root CA certificate |
| **Download Timeout** | Increase timeout, check network |
| **Insufficient Space** | Use smaller firmware or OTA partition |
| **Update Loop** | Check version comparison logic |
| **Server Unreachable** | Verify URL and network connectivity |

### **Debug Commands**
```bash
# Check current status
status
ota_info

# Manual update check
check_update

# Check network connectivity
ping yourwebsite.com
```

---

**🚀 Dengan web-based OTA, Anda dapat mengelola firmware ratusan device ESP32 secara terpusat!**
